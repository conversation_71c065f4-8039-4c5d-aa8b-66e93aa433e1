(()=>{var e={};e.id=334,e.ids=[334],e.modules={440:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>i});var a=t(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3539:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>r.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>c});var a=t(5239),i=t(8088),l=t(8170),r=t.n(l),n=t(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c={children:["",{children:["core-principles",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4866)),"C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\core-principles\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,8042)),"C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\core-principles\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/core-principles/page",pathname:"/core-principles",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},3873:e=>{"use strict";e.exports=require("path")},4287:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,5814,23))},4866:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h,metadata:()=>x});var a=t(7413),i=t(4536),l=t.n(i),r=t(6373);let n=(0,r.A)("layers",[["path",{d:"M12.83 2.18a2 2 0 0 0-1.66 0L2.6 6.08a1 1 0 0 0 0 1.83l8.58 3.91a2 2 0 0 0 1.66 0l8.58-3.9a1 1 0 0 0 0-1.83z",key:"zw3jo"}],["path",{d:"M2 12a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 12",key:"1wduqc"}],["path",{d:"M2 17a1 1 0 0 0 .58.91l8.6 3.91a2 2 0 0 0 1.65 0l8.58-3.9A1 1 0 0 0 22 17",key:"kqbvx6"}]]);var d=t(5838),c=t(6440);let o=(0,r.A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]),m=(0,r.A)("compass",[["path",{d:"m16.24 7.76-1.804 5.411a2 2 0 0 1-1.265 1.265L7.76 16.24l1.804-5.411a2 2 0 0 1 1.265-1.265z",key:"9ktpf1"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]),x={title:"Core Principles of the El-Gilani Methodology | EGM Foundations",description:"Explore the fundamental principles of the El-Gilani Methodology, including the non-physical body, qalb (esoteric heart), and spiritual diseases."};function h(){return(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsxs)("section",{className:"relative bg-gradient-to-br from-deep-indigo via-primary-teal to-accent-gold text-white py-20 lg:py-32",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,a.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"font-serif text-4xl md:text-6xl lg:text-7xl font-bold mb-6",children:"Core Principles"}),(0,a.jsx)("p",{className:"text-xl md:text-2xl mb-8 text-primary-teal-light max-w-4xl mx-auto",children:"The Fundamental Foundations of the El-Gilani Methodology"})]})})]}),(0,a.jsx)("section",{className:"py-16 lg:py-24 bg-white",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsx)("h2",{className:"font-serif text-3xl md:text-4xl font-bold text-dark-gray mb-6",children:"Understanding the Human Being"}),(0,a.jsx)("p",{className:"text-lg text-medium-gray max-w-4xl mx-auto",children:"The El-Gilani Methodology is built upon a profound understanding of human nature that recognizes both physical and non-physical dimensions of existence. This holistic view forms the foundation for all diagnostic and therapeutic approaches."})]})})}),(0,a.jsx)("section",{className:"py-16 lg:py-24 bg-warm-gray",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"font-serif text-3xl md:text-4xl font-bold text-dark-gray mb-6",children:"The Non-Physical Body"}),(0,a.jsx)("p",{className:"text-lg text-medium-gray mb-6",children:"Central to EGM is the recognition that human beings possess both a physical body and a non-physical body. While conventional medicine focuses exclusively on the physical, EGM understands that many illnesses originate in the non-physical realm."}),(0,a.jsx)("p",{className:"text-lg text-medium-gray mb-6",children:"The non-physical body is the seat of emotions, thoughts, and spiritual experiences. It is here that many mental and even physical ailments first manifest before affecting the physical body."}),(0,a.jsx)("p",{className:"text-lg text-medium-gray mb-8",children:"Understanding and treating diseases of the non-physical body is essential for achieving true healing and preventing the recurrence of illness."})]}),(0,a.jsxs)("div",{className:"bg-white p-8 rounded-lg shadow-sm",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)(n,{className:"w-16 h-16 text-primary-teal mx-auto mb-4"}),(0,a.jsx)("h3",{className:"font-serif text-2xl font-semibold text-dark-gray",children:"Dual Nature of Existence"})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"border-l-4 border-primary-teal pl-4",children:[(0,a.jsx)("h4",{className:"font-semibold text-dark-gray mb-2",children:"Physical Body"}),(0,a.jsx)("p",{className:"text-sm text-medium-gray",children:"The material form that interacts with the physical world"})]}),(0,a.jsxs)("div",{className:"border-l-4 border-accent-gold pl-4",children:[(0,a.jsx)("h4",{className:"font-semibold text-dark-gray mb-2",children:"Non-Physical Body"}),(0,a.jsx)("p",{className:"text-sm text-medium-gray",children:"The spiritual form that houses consciousness, emotions, and the soul"})]})]})]})]})})}),(0,a.jsx)("section",{className:"py-16 lg:py-24 bg-white",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,a.jsxs)("div",{className:"order-2 lg:order-1 bg-gradient-to-br from-deep-indigo to-primary-teal p-8 rounded-lg text-white",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)(d.A,{className:"w-16 h-16 text-accent-gold mx-auto mb-4"}),(0,a.jsx)("h3",{className:"font-serif text-2xl font-semibold",children:"The Qalb (Esoteric Heart)"})]}),(0,a.jsx)("p",{className:"mb-4",children:"The qalb is the spiritual heart, distinct from the physical heart. It is the center of spiritual perception, divine connection, and moral consciousness."}),(0,a.jsxs)("ul",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-accent-gold rounded-full mt-2 flex-shrink-0"}),(0,a.jsx)("span",{children:"Center of spiritual awareness and divine connection"})]}),(0,a.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-accent-gold rounded-full mt-2 flex-shrink-0"}),(0,a.jsx)("span",{children:"Source of moral and ethical guidance"})]}),(0,a.jsxs)("li",{className:"flex items-start space-x-2",children:[(0,a.jsx)("div",{className:"w-1.5 h-1.5 bg-accent-gold rounded-full mt-2 flex-shrink-0"}),(0,a.jsx)("span",{children:"Key to understanding spiritual diseases and their cures"})]})]})]}),(0,a.jsxs)("div",{className:"order-1 lg:order-2",children:[(0,a.jsx)("h2",{className:"font-serif text-3xl md:text-4xl font-bold text-dark-gray mb-6",children:"The Spiritual Heart"}),(0,a.jsx)("p",{className:"text-lg text-medium-gray mb-6",children:"In Islamic spiritual tradition, the qalb (esoteric heart) is recognized as the center of spiritual life. Unlike the physical heart that pumps blood, the qalb is the organ of spiritual perception and divine connection."}),(0,a.jsx)("p",{className:"text-lg text-medium-gray mb-6",children:"EGM recognizes that diseases of the qalb are often the root cause of mental and emotional disorders. When the spiritual heart is diseased - through sin, spiritual neglect, or disconnection from the Divine - it affects the entire being."}),(0,a.jsx)("p",{className:"text-lg text-medium-gray mb-8",children:"Healing the qalb through spiritual purification, remembrance of Allah (dhikr), and moral reformation is central to the EGM approach."}),(0,a.jsxs)(l(),{href:"/treatment-modalities",className:"inline-flex items-center text-primary-teal hover:text-primary-teal-dark font-semibold",children:["Explore Treatment Methods",(0,a.jsx)(c.A,{className:"ml-2 w-4 h-4"})]})]})]})})}),(0,a.jsx)("section",{className:"py-16 lg:py-24 bg-warm-gray",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsx)("h2",{className:"font-serif text-3xl md:text-4xl font-bold text-dark-gray mb-6",children:"Understanding Spiritual Diseases"}),(0,a.jsx)("p",{className:"text-lg text-medium-gray max-w-4xl mx-auto",children:"EGM identifies various spiritual diseases that affect the non-physical body and qalb, leading to mental, emotional, and even physical ailments."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:[(0,a.jsx)(o,{className:"w-12 h-12 text-primary-teal mb-4"}),(0,a.jsx)("h3",{className:"font-semibold text-lg text-dark-gray mb-3",children:"Diseases of the Mind"}),(0,a.jsx)("p",{className:"text-medium-gray text-sm mb-4",children:"Negative thought patterns, obsessions, and mental disturbances that originate from spiritual imbalance."}),(0,a.jsxs)("ul",{className:"text-xs text-medium-gray space-y-1",children:[(0,a.jsx)("li",{children:"• Excessive worry and anxiety"}),(0,a.jsx)("li",{children:"• Obsessive thoughts"}),(0,a.jsx)("li",{children:"• Mental confusion and fog"})]})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:[(0,a.jsx)(d.A,{className:"w-12 h-12 text-accent-gold mb-4"}),(0,a.jsx)("h3",{className:"font-semibold text-lg text-dark-gray mb-3",children:"Diseases of the Heart"}),(0,a.jsx)("p",{className:"text-medium-gray text-sm mb-4",children:"Spiritual ailments that affect the qalb, including pride, envy, hatred, and disconnection from the Divine."}),(0,a.jsxs)("ul",{className:"text-xs text-medium-gray space-y-1",children:[(0,a.jsx)("li",{children:"• Spiritual pride and arrogance"}),(0,a.jsx)("li",{children:"• Envy and jealousy"}),(0,a.jsx)("li",{children:"• Hatred and resentment"})]})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:[(0,a.jsx)(m,{className:"w-12 h-12 text-deep-indigo mb-4"}),(0,a.jsx)("h3",{className:"font-semibold text-lg text-dark-gray mb-3",children:"Diseases of the Soul"}),(0,a.jsx)("p",{className:"text-medium-gray text-sm mb-4",children:"Deeper spiritual ailments that affect the very essence of the person, including moral corruption and spiritual blindness."}),(0,a.jsxs)("ul",{className:"text-xs text-medium-gray space-y-1",children:[(0,a.jsx)("li",{children:"• Moral corruption"}),(0,a.jsx)("li",{children:"• Spiritual blindness"}),(0,a.jsx)("li",{children:"• Loss of divine connection"})]})]})]})]})}),(0,a.jsx)("section",{className:"py-16 lg:py-24 bg-white",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsx)("h2",{className:"font-serif text-3xl md:text-4xl font-bold text-dark-gray mb-6",children:"A Holistic Understanding"}),(0,a.jsx)("p",{className:"text-lg text-medium-gray max-w-4xl mx-auto",children:"EGM’s approach recognizes the interconnectedness of all aspects of human existence, understanding that true healing must address the whole person."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-12 items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-serif text-2xl font-semibold text-dark-gray mb-6",children:"Interconnected Dimensions"}),(0,a.jsx)("p",{className:"text-lg text-medium-gray mb-6",children:"The physical and non-physical bodies are intimately connected. Diseases in one realm inevitably affect the other. This understanding allows EGM to address root causes rather than merely treating symptoms."}),(0,a.jsx)("p",{className:"text-lg text-medium-gray mb-8",children:"By healing the spiritual dimensions of illness, EGM often achieves remarkable results where conventional treatments have failed, demonstrating the profound connection between spiritual health and overall well-being."}),(0,a.jsxs)(l(),{href:"/diagnostic-techniques",className:"inline-flex items-center text-primary-teal hover:text-primary-teal-dark font-semibold",children:["Learn About Diagnostics",(0,a.jsx)(c.A,{className:"ml-2 w-4 h-4"})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-primary-teal to-deep-indigo p-8 rounded-lg text-white",children:[(0,a.jsx)("h4",{className:"font-serif text-xl font-semibold mb-6 text-center",children:"Key Principles"}),(0,a.jsxs)("ul",{className:"space-y-4",children:[(0,a.jsxs)("li",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-accent-gold rounded-full mt-2 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-sm",children:"Recognition of both physical and non-physical dimensions"})]}),(0,a.jsxs)("li",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-accent-gold rounded-full mt-2 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-sm",children:"Understanding the qalb as the center of spiritual health"})]}),(0,a.jsxs)("li",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-accent-gold rounded-full mt-2 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-sm",children:"Identification and treatment of spiritual diseases"})]}),(0,a.jsxs)("li",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-accent-gold rounded-full mt-2 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-sm",children:"Faith-based healing rooted in Islamic spiritual sciences"})]})]})]})]})]})}),(0,a.jsx)("section",{className:"py-16 lg:py-24 bg-primary-teal text-white",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,a.jsx)("h2",{className:"font-serif text-3xl md:text-4xl font-bold mb-6",children:"Experience the Methodology"}),(0,a.jsx)("p",{className:"text-xl mb-8 max-w-3xl mx-auto",children:"Discover how these principles are applied in practice through EGM’s unique diagnostic techniques and treatment modalities."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsxs)(l(),{href:"/diagnostic-techniques",className:"inline-flex items-center px-8 py-3 bg-accent-gold hover:bg-accent-gold-dark text-white font-semibold rounded-lg transition-colors duration-200",children:["Diagnostic Techniques",(0,a.jsx)(c.A,{className:"ml-2 w-5 h-5"})]}),(0,a.jsx)(l(),{href:"/treatment-modalities",className:"inline-flex items-center px-8 py-3 border-2 border-white text-white hover:bg-white hover:text-primary-teal font-semibold rounded-lg transition-colors duration-200",children:"Treatment Methods"})]})]})})]})}},6440:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(6373).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},7495:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,4536,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[447,721,658,400],()=>t(3539));module.exports=a})();