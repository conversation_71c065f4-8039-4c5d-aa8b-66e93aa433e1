{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/AI%20Coding%20Projects/IQOU/EGM/egm-website/src/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { <PERSON><PERSON>, X, Heart } from 'lucide-react';\n\nconst navigationItems = [\n  { href: '/', label: 'Home' },\n  { href: '/about-sheikh', label: 'About El-Sheikh' },\n  { href: '/origins', label: 'Origins' },\n  { href: '/core-principles', label: 'Core Principles' },\n  { href: '/treatment-modalities', label: 'Treatment' },\n  { href: '/diagnostic-techniques', label: 'Diagnostics' },\n  { href: '/perspective-on-psychiatry', label: 'Perspective' },\n  { href: '/non-physical-realities', label: 'Non-Physical Realities' },\n  { href: '/spiritual-basis', label: 'Spiritual Basis' },\n  { href: '/journey-of-soul', label: 'Journey of Soul' },\n  { href: '/case-studies', label: 'Case Studies' },\n  { href: '/physical-ailments', label: 'Physical Ailments' },\n  { href: '/call-to-humanity', label: 'Call to Humanity' },\n  { href: '/contact', label: 'Contact' },\n];\n\nexport default function Navigation() {\n  const [isOpen, setIsOpen] = useState(false);\n\n  return (\n    <nav className=\"bg-white shadow-lg sticky top-0 z-50\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between items-center h-16\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-gradient-to-br from-primary-teal to-accent-gold rounded-full flex items-center justify-center\">\n              <Heart className=\"w-5 h-5 text-white\" />\n            </div>\n            <span className=\"font-serif font-bold text-xl text-primary-teal\">\n              El-Gilani Methodology\n            </span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden lg:flex items-center space-x-1\">\n            {navigationItems.slice(0, 7).map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"px-3 py-2 text-sm font-medium text-gray-700 hover:text-primary-teal hover:bg-warm-gray rounded-md transition-colors duration-200\"\n              >\n                {item.label}\n              </Link>\n            ))}\n            \n            {/* More dropdown for remaining items */}\n            <div className=\"relative group\">\n              <button className=\"px-3 py-2 text-sm font-medium text-gray-700 hover:text-primary-teal hover:bg-warm-gray rounded-md transition-colors duration-200\">\n                More\n              </button>\n              <div className=\"absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50\">\n                <div className=\"py-1\">\n                  {navigationItems.slice(7).map((item) => (\n                    <Link\n                      key={item.href}\n                      href={item.href}\n                      className=\"block px-4 py-2 text-sm text-gray-700 hover:bg-warm-gray hover:text-primary-teal\"\n                    >\n                      {item.label}\n                    </Link>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"lg:hidden\">\n            <button\n              onClick={() => setIsOpen(!isOpen)}\n              className=\"p-2 rounded-md text-gray-700 hover:text-primary-teal hover:bg-warm-gray focus:outline-none focus:ring-2 focus:ring-primary-teal\"\n            >\n              {isOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n            </button>\n          </div>\n        </div>\n\n        {/* Mobile Navigation */}\n        {isOpen && (\n          <div className=\"lg:hidden\">\n            <div className=\"px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200\">\n              {navigationItems.map((item) => (\n                <Link\n                  key={item.href}\n                  href={item.href}\n                  className=\"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-teal hover:bg-warm-gray rounded-md transition-colors duration-200\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  {item.label}\n                </Link>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAJA;;;;;AAMA,MAAM,kBAAkB;IACtB;QAAE,MAAM;QAAK,OAAO;IAAO;IAC3B;QAAE,MAAM;QAAiB,OAAO;IAAkB;IAClD;QAAE,MAAM;QAAY,OAAO;IAAU;IACrC;QAAE,MAAM;QAAoB,OAAO;IAAkB;IACrD;QAAE,MAAM;QAAyB,OAAO;IAAY;IACpD;QAAE,MAAM;QAA0B,OAAO;IAAc;IACvD;QAAE,MAAM;QAA8B,OAAO;IAAc;IAC3D;QAAE,MAAM;QAA2B,OAAO;IAAyB;IACnE;QAAE,MAAM;QAAoB,OAAO;IAAkB;IACrD;QAAE,MAAM;QAAoB,OAAO;IAAkB;IACrD;QAAE,MAAM;QAAiB,OAAO;IAAe;IAC/C;QAAE,MAAM;QAAsB,OAAO;IAAoB;IACzD;QAAE,MAAM;QAAqB,OAAO;IAAmB;IACvD;QAAE,MAAM;QAAY,OAAO;IAAU;CACtC;AAEc,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC;oCAAK,WAAU;8CAAiD;;;;;;;;;;;;sCAMnE,8OAAC;4BAAI,WAAU;;gCACZ,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,qBAChC,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,KAAK;uCAJN,KAAK,IAAI;;;;;8CASlB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAO,WAAU;sDAAmI;;;;;;sDAGrJ,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACZ,gBAAgB,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,qBAC7B,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAU;kEAET,KAAK,KAAK;uDAJN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAa1B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,UAAU,CAAC;gCAC1B,WAAU;0CAET,uBAAS,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;gBAM3D,wBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;gCAEH,MAAM,KAAK,IAAI;gCACf,WAAU;gCACV,SAAS,IAAM,UAAU;0CAExB,KAAK,KAAK;+BALN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;AAchC", "debugId": null}}]}