import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Spiritual Basis | El-Gilani Methodology',
  description: 'The connection between faith, morality, and well-being in EGM.',
};

export default function SpiritualBasis() {
  return (
    <div className="min-h-screen">
      <section className="relative bg-primary-teal text-white py-20 lg:py-32">
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="font-serif text-4xl md:text-6xl lg:text-7xl font-bold mb-6">
              Spiritual Basis
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-accent-gold font-semibold">
              Faith, Morality, and Well-being
            </p>
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="font-serif text-3xl md:text-4xl font-bold text-dark-gray mb-6">
              The Foundation of Healing
            </h2>
            <p className="text-lg text-medium-gray max-w-4xl mx-auto">
              EGM is built upon the understanding that spiritual health is fundamental 
              to overall well-being, and that faith and morality play crucial roles 
              in maintaining mental and physical health.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}
