[{"C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\about-sheikh\\page.tsx": "1", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\call-to-humanity\\page.tsx": "2", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\case-studies\\page.tsx": "3", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\contact\\page.tsx": "4", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\core-principles\\page.tsx": "5", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\diagnostic-techniques\\page.tsx": "6", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\journey-of-soul\\page.tsx": "7", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\layout.tsx": "8", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\non-physical-realities\\page.tsx": "9", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\origins\\page.tsx": "10", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\page.tsx": "11", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\perspective-on-psychiatry\\page.tsx": "12", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\physical-ailments\\page.tsx": "13", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\spiritual-basis\\page.tsx": "14", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\treatment-modalities\\page.tsx": "15", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\components\\Footer.tsx": "16", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\components\\Navigation.tsx": "17"}, {"size": 10258, "mtime": 1748444545796, "results": "18", "hashOfConfig": "19"}, {"size": 1981, "mtime": 1748445609798, "results": "20", "hashOfConfig": "19"}, {"size": 12350, "mtime": 1748445449689, "results": "21", "hashOfConfig": "19"}, {"size": 15023, "mtime": 1748439352798, "results": "22", "hashOfConfig": "19"}, {"size": 15407, "mtime": 1748444624781, "results": "23", "hashOfConfig": "19"}, {"size": 12771, "mtime": 1748445388879, "results": "24", "hashOfConfig": "19"}, {"size": 1479, "mtime": 1748445570964, "results": "25", "hashOfConfig": "19"}, {"size": 1914, "mtime": 1748437294397, "results": "26", "hashOfConfig": "19"}, {"size": 1551, "mtime": 1748445511645, "results": "27", "hashOfConfig": "19"}, {"size": 14245, "mtime": 1748444578379, "results": "28", "hashOfConfig": "19"}, {"size": 8385, "mtime": 1748445261477, "results": "29", "hashOfConfig": "19"}, {"size": 3798, "mtime": 1748445495032, "results": "30", "hashOfConfig": "19"}, {"size": 1536, "mtime": 1748445589830, "results": "31", "hashOfConfig": "19"}, {"size": 1519, "mtime": 1748445524572, "results": "32", "hashOfConfig": "19"}, {"size": 12317, "mtime": 1748445663232, "results": "33", "hashOfConfig": "19"}, {"size": 4420, "mtime": 1748438296399, "results": "34", "hashOfConfig": "19"}, {"size": 4371, "mtime": 1748444436604, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "6ljw0s", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\about-sheikh\\page.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\call-to-humanity\\page.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\case-studies\\page.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\contact\\page.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\core-principles\\page.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\diagnostic-techniques\\page.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\journey-of-soul\\page.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\layout.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\non-physical-realities\\page.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\origins\\page.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\page.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\perspective-on-psychiatry\\page.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\physical-ailments\\page.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\spiritual-basis\\page.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\treatment-modalities\\page.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\components\\Footer.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\components\\Navigation.tsx", [], []]