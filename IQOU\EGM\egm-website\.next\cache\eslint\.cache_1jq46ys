[{"C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\about-sheikh\\page.tsx": "1", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\contact\\page.tsx": "2", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\core-principles\\page.tsx": "3", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\layout.tsx": "4", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\origins\\page.tsx": "5", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\page.tsx": "6", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\components\\Footer.tsx": "7", "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\components\\Navigation.tsx": "8"}, {"size": 10258, "mtime": 1748444545796, "results": "9", "hashOfConfig": "10"}, {"size": 15023, "mtime": 1748439352798, "results": "11", "hashOfConfig": "10"}, {"size": 15407, "mtime": 1748444624781, "results": "12", "hashOfConfig": "10"}, {"size": 1914, "mtime": 1748437294397, "results": "13", "hashOfConfig": "10"}, {"size": 14245, "mtime": 1748444578379, "results": "14", "hashOfConfig": "10"}, {"size": 8519, "mtime": 1748444926259, "results": "15", "hashOfConfig": "10"}, {"size": 4420, "mtime": 1748438296399, "results": "16", "hashOfConfig": "10"}, {"size": 4371, "mtime": 1748444436604, "results": "17", "hashOfConfig": "10"}, {"filePath": "18", "messages": "19", "suppressedMessages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "6ljw0s", {"filePath": "21", "messages": "22", "suppressedMessages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\about-sheikh\\page.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\contact\\page.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\core-principles\\page.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\layout.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\origins\\page.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\page.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\components\\Footer.tsx", [], [], "C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\components\\Navigation.tsx", [], []]