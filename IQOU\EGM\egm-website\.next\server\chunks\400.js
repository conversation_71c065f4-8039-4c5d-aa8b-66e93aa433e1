exports.id=400,exports.ids=[400],exports.modules={112:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,5814,23)),Promise.resolve().then(t.bind(t,6246))},647:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6444,23)),Promise.resolve().then(t.t.bind(t,6042,23)),Promise.resolve().then(t.t.bind(t,8170,23)),Promise.resolve().then(t.t.bind(t,9477,23)),Promise.resolve().then(t.t.bind(t,9345,23)),Promise.resolve().then(t.t.bind(t,2089,23)),Promise.resolve().then(t.t.bind(t,6577,23)),Promise.resolve().then(t.t.bind(t,1307,23))},1135:()=>{},2495:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,6346,23)),Promise.resolve().then(t.t.bind(t,7924,23)),Promise.resolve().then(t.t.bind(t,5656,23)),Promise.resolve().then(t.t.bind(t,99,23)),Promise.resolve().then(t.t.bind(t,8243,23)),Promise.resolve().then(t.t.bind(t,8827,23)),Promise.resolve().then(t.t.bind(t,2763,23)),Promise.resolve().then(t.t.bind(t,7173,23))},2912:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,4536,23)),Promise.resolve().then(t.bind(t,8928))},6246:(e,s,t)=>{"use strict";t.d(s,{default:()=>d});var i=t(687),l=t(3210),a=t(5814),r=t.n(a),n=t(7760),o=t(1860),c=t(2941);let h=[{href:"/",label:"Home"},{href:"/about-sheikh",label:"About El-Sheikh"},{href:"/origins",label:"Origins"},{href:"/core-principles",label:"Core Principles"},{href:"/treatment-modalities",label:"Treatment"},{href:"/diagnostic-techniques",label:"Diagnostics"},{href:"/perspective-on-psychiatry",label:"Perspective"},{href:"/non-physical-realities",label:"Non-Physical Realities"},{href:"/spiritual-basis",label:"Spiritual Basis"},{href:"/journey-of-soul",label:"Journey of Soul"},{href:"/case-studies",label:"Case Studies"},{href:"/physical-ailments",label:"Physical Ailments"},{href:"/call-to-humanity",label:"Call to Humanity"},{href:"/contact",label:"Contact"}];function d(){let[e,s]=(0,l.useState)(!1);return(0,i.jsx)("nav",{className:"bg-white shadow-lg sticky top-0 z-50",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,i.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,i.jsxs)(r(),{href:"/",className:"flex items-center space-x-2",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-primary-teal to-accent-gold rounded-full flex items-center justify-center",children:(0,i.jsx)(n.A,{className:"w-5 h-5 text-white"})}),(0,i.jsx)("span",{className:"font-serif font-bold text-xl text-primary-teal",children:"El-Gilani Methodology"})]}),(0,i.jsxs)("div",{className:"hidden lg:flex items-center space-x-1",children:[h.slice(0,7).map(e=>(0,i.jsx)(r(),{href:e.href,className:"px-3 py-2 text-sm font-medium text-gray-700 hover:text-primary-teal hover:bg-warm-gray rounded-md transition-colors duration-200",children:e.label},e.href)),(0,i.jsxs)("div",{className:"relative group",children:[(0,i.jsx)("button",{className:"px-3 py-2 text-sm font-medium text-gray-700 hover:text-primary-teal hover:bg-warm-gray rounded-md transition-colors duration-200",children:"More"}),(0,i.jsx)("div",{className:"absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50",children:(0,i.jsx)("div",{className:"py-1",children:h.slice(7).map(e=>(0,i.jsx)(r(),{href:e.href,className:"block px-4 py-2 text-sm text-gray-700 hover:bg-warm-gray hover:text-primary-teal",children:e.label},e.href))})})]})]}),(0,i.jsx)("div",{className:"lg:hidden",children:(0,i.jsx)("button",{onClick:()=>s(!e),className:"p-2 rounded-md text-gray-700 hover:text-primary-teal hover:bg-warm-gray focus:outline-none focus:ring-2 focus:ring-primary-teal",children:e?(0,i.jsx)(o.A,{className:"w-6 h-6"}):(0,i.jsx)(c.A,{className:"w-6 h-6"})})})]}),e&&(0,i.jsx)("div",{className:"lg:hidden",children:(0,i.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200",children:h.map(e=>(0,i.jsx)(r(),{href:e.href,className:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-teal hover:bg-warm-gray rounded-md transition-colors duration-200",onClick:()=>s(!1),children:e.label},e.href))})})]})})}},8042:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>y,metadata:()=>g});var i=t(7413),l=t(5230),a=t.n(l),r=t(3529),n=t.n(r);t(1135);var o=t(8928),c=t(4536),h=t.n(c),d=t(5838),m=t(2777),x=t(343);function p(){let e=new Date().getFullYear();return(0,i.jsx)("footer",{className:"bg-dark-gray text-white",children:(0,i.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12",children:[(0,i.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,i.jsxs)("div",{className:"lg:col-span-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,i.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-primary-teal to-accent-gold rounded-full flex items-center justify-center",children:(0,i.jsx)(d.A,{className:"w-5 h-5 text-white"})}),(0,i.jsx)("span",{className:"font-serif font-bold text-xl",children:"El-Gilani Methodology"})]}),(0,i.jsx)("p",{className:"text-gray-300 mb-4 max-w-md",children:"A revolutionary healing system rooted in the wisdom of the Holy Quran and Sufi spiritual sciences, developed by El-Sheikh Syed Mubarik Ali Shah Gilani."}),(0,i.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[(0,i.jsx)("span",{children:"“Allah is the only one who heals”"}),(0,i.jsx)("span",{children:"- El-Sheikh Gilani"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-semibold text-lg mb-4 text-accent-gold",children:"Quick Links"}),(0,i.jsxs)("ul",{className:"space-y-2",children:[(0,i.jsx)("li",{children:(0,i.jsx)(h(),{href:"/about-sheikh",className:"text-gray-300 hover:text-primary-teal-light transition-colors",children:"About El-Sheikh"})}),(0,i.jsx)("li",{children:(0,i.jsx)(h(),{href:"/core-principles",className:"text-gray-300 hover:text-primary-teal-light transition-colors",children:"Core Principles"})}),(0,i.jsx)("li",{children:(0,i.jsx)(h(),{href:"/treatment-modalities",className:"text-gray-300 hover:text-primary-teal-light transition-colors",children:"Treatment Modalities"})}),(0,i.jsx)("li",{children:(0,i.jsx)(h(),{href:"/case-studies",className:"text-gray-300 hover:text-primary-teal-light transition-colors",children:"Case Studies"})}),(0,i.jsx)("li",{children:(0,i.jsx)(h(),{href:"/contact",className:"text-gray-300 hover:text-primary-teal-light transition-colors",children:"Contact"})})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-semibold text-lg mb-4 text-accent-gold",children:"Resources"}),(0,i.jsxs)("ul",{className:"space-y-2",children:[(0,i.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,i.jsx)(m.A,{className:"w-4 h-4 text-primary-teal-light"}),(0,i.jsx)("span",{className:"text-gray-300",children:"Ultimate Fraud of Freudists"})]}),(0,i.jsxs)("li",{className:"flex items-center space-x-2",children:[(0,i.jsx)(x.A,{className:"w-4 h-4 text-primary-teal-light"}),(0,i.jsx)("a",{href:"mailto:<EMAIL>",className:"text-gray-300 hover:text-primary-teal-light transition-colors",children:"<EMAIL>"})]})]}),(0,i.jsxs)("div",{className:"mt-6",children:[(0,i.jsx)("h4",{className:"font-medium text-sm mb-2 text-gray-400",children:"Sufi Psychiatric Research Institute"}),(0,i.jsx)("p",{className:"text-xs text-gray-500",children:"Established to demonstrate the healing potential of the Holy Quran through scientific methodology."})]})]})]}),(0,i.jsx)("div",{className:"border-t border-gray-700 mt-8 pt-8",children:(0,i.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center",children:[(0,i.jsxs)("div",{className:"text-sm text-gray-400 mb-4 md:mb-0",children:["\xa9 ",e," El-Gilani Methodology. All rights reserved."]}),(0,i.jsx)("div",{className:"text-sm text-gray-400",children:(0,i.jsx)("span",{className:"italic",children:"“Know thyself, and thou shalt know thy Lord.”"})})]})})]})})}let g={title:"El-Gilani Methodology (EGM) - Revolutionary Healing Through Islamic Spiritual Sciences",description:"Discover the El-Gilani Methodology, a profound system of healing developed by El-Sheikh Syed Mubarik Ali Shah Gilani. This faith-centered approach addresses mental and physical ailments through Quranic therapy and Sufi spiritual sciences.",keywords:"El-Gilani Methodology, EGM, Islamic healing, Sufi therapy, Quranic psychiatry, spiritual healing, mental health, El-Sheikh Gilani",authors:[{name:"El-Sheikh Syed Mubarik Ali Shah Gilani"}],openGraph:{title:"El-Gilani Methodology (EGM) - Revolutionary Healing",description:"A comprehensive healing system rooted in Islamic spiritual sciences and Quranic therapy.",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"El-Gilani Methodology (EGM)",description:"Revolutionary healing through Islamic spiritual sciences"},robots:{index:!0,follow:!0}};function y({children:e}){return(0,i.jsx)("html",{lang:"en",className:"scroll-smooth",children:(0,i.jsxs)("body",{className:`${a().variable} ${n().variable} antialiased min-h-screen flex flex-col`,children:[(0,i.jsx)(o.default,{}),(0,i.jsx)("main",{className:"flex-grow",children:e}),(0,i.jsx)(p,{})]})})}},8928:(e,s,t)=>{"use strict";t.d(s,{default:()=>i});let i=(0,t(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\AI Coding Projects\\\\IQOU\\\\EGM\\\\egm-website\\\\src\\\\components\\\\Navigation.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\components\\Navigation.tsx","default")}};