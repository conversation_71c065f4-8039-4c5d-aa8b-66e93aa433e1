import Link from 'next/link';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Stethoscope } from 'lucide-react';

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary-teal via-deep-indigo to-primary-teal-dark text-white py-20 lg:py-32 islamic-pattern">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="font-serif text-4xl md:text-6xl lg:text-7xl font-bold mb-6">
              Rediscover Healing
            </h1>
            <p className="text-xl md:text-2xl lg:text-3xl mb-4 text-primary-teal-light">
              The El-Gilani Methodology
            </p>
            <p className="text-lg md:text-xl max-w-3xl mx-auto mb-8 text-gray-200">
              A profound system of healing developed by El-Sheikh <PERSON>.
              This methodology offers a unique, faith-centered approach to understanding and treating
              a wide range of mental and physical ailments.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/about-sheikh"
                className="inline-flex items-center px-8 py-3 bg-accent-gold hover:bg-accent-gold-dark text-white font-semibold rounded-lg transition-colors duration-200"
              >
                Learn About El-Sheikh
                <ArrowRight className="ml-2 w-5 h-5" />
              </Link>
              <Link
                href="/core-principles"
                className="inline-flex items-center px-8 py-3 border-2 border-white text-white hover:bg-white hover:text-primary-teal font-semibold rounded-lg transition-colors duration-200"
              >
                Explore EGM Principles
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* What is EGM Section */}
      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="font-serif text-3xl md:text-4xl lg:text-5xl font-bold text-dark-gray mb-6">
              What is the El-Gilani Methodology?
            </h2>
            <p className="text-lg md:text-xl text-medium-gray max-w-4xl mx-auto">
              The El-Gilani Methodology is a comprehensive healing system rooted in the wisdom of the Holy Quran
              and Sufi spiritual sciences. It posits that many illnesses, particularly mental disorders, stem from
              diseases of the non-physical self, or psyche.
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="font-serif text-2xl md:text-3xl font-semibold text-primary-teal mb-6">
                A Revolutionary Approach
              </h3>
              <p className="text-medium-gray mb-6">
                Unlike treatments that focus solely on the physical body, EGM seeks to purify the soul and
                restore balance through spiritual means. This methodology recognizes the existence and importance
                of the non-physical body and its esoteric heart (qalb) in health and illness.
              </p>
              <p className="text-medium-gray mb-8">
                "I am not the healer, Allah is the only one who heals and He doesn't need any reward."
                - El-Sheikh Gilani
              </p>
              <Link
                href="/origins"
                className="inline-flex items-center text-primary-teal hover:text-primary-teal-dark font-semibold"
              >
                Discover the Origins
                <ArrowRight className="ml-2 w-4 h-4" />
              </Link>
            </div>
            <div className="bg-warm-gray p-8 rounded-lg">
              <div className="text-center">
                <Heart className="w-16 h-16 text-accent-gold mx-auto mb-4" />
                <h4 className="font-serif text-xl font-semibold text-dark-gray mb-4">
                  Spiritual Healing
                </h4>
                <p className="text-medium-gray">
                  EGM addresses the root causes of illness through spiritual purification,
                  Quranic therapy, and the restoration of divine connection.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Key Differentiators */}
      <section className="py-16 lg:py-24 bg-warm-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="font-serif text-3xl md:text-4xl font-bold text-dark-gray mb-6">
              Key Differentiators
            </h2>
            <p className="text-lg text-medium-gray max-w-3xl mx-auto">
              EGM offers a unique approach that sets it apart from conventional treatments
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white p-6 rounded-lg shadow-sm text-center">
              <Heart className="w-12 h-12 text-primary-teal mx-auto mb-4" />
              <h3 className="font-semibold text-lg text-dark-gray mb-3">Addresses the Soul</h3>
              <p className="text-medium-gray text-sm">
                Recognizes the existence and importance of the non-physical body and its esoteric heart (qalb)
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm text-center">
              <Stethoscope className="w-12 h-12 text-accent-gold mx-auto mb-4" />
              <h3 className="font-semibold text-lg text-dark-gray mb-3">Non-Invasive</h3>
              <p className="text-medium-gray text-sm">
                Employs no drugs or electroshock therapy, relying on recitations, prayers, and visualizations
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm text-center">
              <BookOpen className="w-12 h-12 text-deep-indigo mx-auto mb-4" />
              <h3 className="font-semibold text-lg text-dark-gray mb-3">Faith-Based</h3>
              <p className="text-medium-gray text-sm">
                Grounded in the belief in One Almighty God as the ultimate healer
              </p>
            </div>

            <div className="bg-white p-6 rounded-lg shadow-sm text-center">
              <Users className="w-12 h-12 text-primary-teal-light mx-auto mb-4" />
              <h3 className="font-semibold text-lg text-dark-gray mb-3">Holistic</h3>
              <p className="text-medium-gray text-sm">
                Understands the deep connection between actions, morality, beliefs, and overall well-being
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 lg:py-24 bg-primary-teal text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="font-serif text-3xl md:text-4xl font-bold mb-6">
            Explore the Path to True Healing
          </h2>
          <p className="text-xl mb-8 max-w-3xl mx-auto">
            Discover the revolutionary insights of El-Sheikh Gilani, the scientific demonstrations of EGM's efficacy,
            and a path to true healing and spiritual understanding.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/case-studies"
              className="inline-flex items-center px-8 py-3 bg-accent-gold hover:bg-accent-gold-dark text-white font-semibold rounded-lg transition-colors duration-200"
            >
              View Case Studies
              <ArrowRight className="ml-2 w-5 h-5" />
            </Link>
            <Link
              href="/contact"
              className="inline-flex items-center px-8 py-3 border-2 border-white text-white hover:bg-white hover:text-primary-teal font-semibold rounded-lg transition-colors duration-200"
            >
              Get in Touch
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
