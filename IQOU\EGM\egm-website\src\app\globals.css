@import "tailwindcss";
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

:root {
  --background: #fefefe;
  --foreground: #1f2937;

  /* EGM Color Palette */
  --primary-teal: #0f766e;
  --primary-teal-light: #14b8a6;
  --primary-teal-dark: #134e4a;

  --accent-gold: #d97706;
  --accent-gold-light: #f59e0b;
  --accent-gold-dark: #92400e;

  --deep-indigo: #1e3a8a;
  --deep-indigo-light: #3b82f6;
  --deep-indigo-dark: #1e40af;

  --warm-gray: #f8fafc;
  --medium-gray: #64748b;
  --dark-gray: #1f2937;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);

  /* Custom Colors */
  --color-primary-teal: var(--primary-teal);
  --color-primary-teal-light: var(--primary-teal-light);
  --color-primary-teal-dark: var(--primary-teal-dark);

  --color-accent-gold: var(--accent-gold);
  --color-accent-gold-light: var(--accent-gold-light);
  --color-accent-gold-dark: var(--accent-gold-dark);

  --color-deep-indigo: var(--deep-indigo);
  --color-deep-indigo-light: var(--deep-indigo-light);
  --color-deep-indigo-dark: var(--deep-indigo-dark);

  --color-warm-gray: var(--warm-gray);
  --color-medium-gray: var(--medium-gray);
  --color-dark-gray: var(--dark-gray);

  /* Typography */
  --font-sans: 'Inter', var(--font-geist-sans), system-ui, sans-serif;
  --font-serif: 'Playfair Display', Georgia, serif;
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans);
  line-height: 1.6;
}

/* Islamic Pattern Background */
.islamic-pattern {
  background-image:
    radial-gradient(circle at 25% 25%, rgba(15, 118, 110, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(217, 151, 6, 0.05) 0%, transparent 50%);
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--warm-gray);
}

::-webkit-scrollbar-thumb {
  background: var(--medium-gray);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-teal);
}
