'use client';

import { useState } from 'react';
import Link from 'next/link';
import { <PERSON><PERSON>, X, Heart } from 'lucide-react';

const navigationItems = [
  { href: '/', label: 'Home' },
  { href: '/about-sheikh.html', label: 'About El-Sheikh' },
  { href: '/origins.html', label: 'Origins' },
  { href: '/core-principles.html', label: 'Core Principles' },
  { href: '/treatment-modalities.html', label: 'Treatment' },
  { href: '/diagnostic-techniques.html', label: 'Diagnostics' },
  { href: '/perspective-on-psychiatry.html', label: 'Perspective' },
  { href: '/non-physical-realities.html', label: 'Non-Physical Realities' },
  { href: '/spiritual-basis.html', label: 'Spiritual Basis' },
  { href: '/journey-of-soul.html', label: 'Journey of Soul' },
  { href: '/case-studies.html', label: 'Case Studies' },
  { href: '/physical-ailments.html', label: 'Physical Ailments' },
  { href: '/call-to-humanity.html', label: 'Call to Humanity' },
  { href: '/contact.html', label: 'Contact' },
];

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <nav className="bg-white shadow-lg sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-gradient-to-br from-primary-teal to-accent-gold rounded-full flex items-center justify-center">
              <Heart className="w-5 h-5 text-white" />
            </div>
            <span className="font-serif font-bold text-xl text-primary-teal">
              El-Gilani Methodology
            </span>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-1">
            {navigationItems.slice(0, 7).map((item) => (
              <Link
                key={item.href}
                href={item.href}
                className="px-3 py-2 text-sm font-medium text-gray-700 hover:text-primary-teal hover:bg-warm-gray rounded-md transition-colors duration-200"
              >
                {item.label}
              </Link>
            ))}

            {/* More dropdown for remaining items */}
            <div className="relative group">
              <button className="px-3 py-2 text-sm font-medium text-gray-700 hover:text-primary-teal hover:bg-warm-gray rounded-md transition-colors duration-200">
                More
              </button>
              <div className="absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                <div className="py-1">
                  {navigationItems.slice(7).map((item) => (
                    <Link
                      key={item.href}
                      href={item.href}
                      className="block px-4 py-2 text-sm text-gray-700 hover:bg-warm-gray hover:text-primary-teal"
                    >
                      {item.label}
                    </Link>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Mobile menu button */}
          <div className="lg:hidden">
            <button
              onClick={() => setIsOpen(!isOpen)}
              className="p-2 rounded-md text-gray-700 hover:text-primary-teal hover:bg-warm-gray focus:outline-none focus:ring-2 focus:ring-primary-teal"
            >
              {isOpen ? <X className="w-6 h-6" /> : <Menu className="w-6 h-6" />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isOpen && (
          <div className="lg:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200">
              {navigationItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-teal hover:bg-warm-gray rounded-md transition-colors duration-200"
                  onClick={() => setIsOpen(false)}
                >
                  {item.label}
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}
