import Link from 'next/link';
import { Heart, Mail, Book } from 'lucide-react';

export default function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-dark-gray text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-gradient-to-br from-primary-teal to-accent-gold rounded-full flex items-center justify-center">
                <Heart className="w-5 h-5 text-white" />
              </div>
              <span className="font-serif font-bold text-xl">
                El-Gilani Methodology
              </span>
            </div>
            <p className="text-gray-300 mb-4 max-w-md">
              A revolutionary healing system rooted in the wisdom of the Holy Quran and Sufi spiritual sciences,
              developed by El-<PERSON>.
            </p>
            <div className="flex items-center space-x-2 text-sm text-gray-400">
              <span>&ldquo;Allah is the only one who heals&rdquo;</span>
              <span>- El-Sheikh Gilani</span>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold text-lg mb-4 text-accent-gold">Quick Links</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/about-sheikh" className="text-gray-300 hover:text-primary-teal-light transition-colors">
                  About El-Sheikh
                </Link>
              </li>
              <li>
                <Link href="/core-principles" className="text-gray-300 hover:text-primary-teal-light transition-colors">
                  Core Principles
                </Link>
              </li>
              <li>
                <Link href="/treatment-modalities" className="text-gray-300 hover:text-primary-teal-light transition-colors">
                  Treatment Modalities
                </Link>
              </li>
              <li>
                <Link href="/case-studies" className="text-gray-300 hover:text-primary-teal-light transition-colors">
                  Case Studies
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-300 hover:text-primary-teal-light transition-colors">
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="font-semibold text-lg mb-4 text-accent-gold">Resources</h3>
            <ul className="space-y-2">
              <li className="flex items-center space-x-2">
                <Book className="w-4 h-4 text-primary-teal-light" />
                <span className="text-gray-300">Ultimate Fraud of Freudists</span>
              </li>
              <li className="flex items-center space-x-2">
                <Mail className="w-4 h-4 text-primary-teal-light" />
                <a
                  href="mailto:<EMAIL>"
                  className="text-gray-300 hover:text-primary-teal-light transition-colors"
                >
                  <EMAIL>
                </a>
              </li>
            </ul>

            <div className="mt-6">
              <h4 className="font-medium text-sm mb-2 text-gray-400">Sufi Psychiatric Research Institute</h4>
              <p className="text-xs text-gray-500">
                Established to demonstrate the healing potential of the Holy Quran through scientific methodology.
              </p>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-700 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-sm text-gray-400 mb-4 md:mb-0">
              © {currentYear} El-Gilani Methodology. All rights reserved.
            </div>
            <div className="text-sm text-gray-400">
              <span className="italic">&ldquo;Know thyself, and thou shalt know thy Lord.&rdquo;</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
