import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Physical Ailments | El-Gilani Methodology',
  description: 'EGM applications to physical conditions like sickle cell anemia and other diseases.',
};

export default function PhysicalAilments() {
  return (
    <div className="min-h-screen">
      <section className="relative bg-primary-teal text-white py-20 lg:py-32">
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="font-serif text-4xl md:text-6xl lg:text-7xl font-bold mb-6">
              Physical Ailments
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-accent-gold font-semibold">
              Spiritual Healing for Physical Conditions
            </p>
          </div>
        </div>
      </section>

      <section className="py-16 lg:py-24 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="font-serif text-3xl md:text-4xl font-bold text-dark-gray mb-6">
              Beyond Mental Health
            </h2>
            <p className="text-lg text-medium-gray max-w-4xl mx-auto">
              EGM has demonstrated effectiveness in treating various physical conditions, 
              including chronic diseases like sickle cell anemia, by addressing their 
              spiritual root causes.
            </p>
          </div>
        </div>
      </section>
    </div>
  );
}
