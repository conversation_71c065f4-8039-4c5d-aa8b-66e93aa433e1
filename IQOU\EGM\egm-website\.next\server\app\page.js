(()=>{var e={};e.id=974,e.ids=[974],e.modules={440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(1658);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1382:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(6373).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4122:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var a=s(7413),r=s(4536),i=s.n(r),l=s(6440),n=s(5838);let o=(0,s(6373).A)("stethoscope",[["path",{d:"M11 2v2",key:"1539x4"}],["path",{d:"M5 2v2",key:"1yf1q8"}],["path",{d:"M5 3H4a2 2 0 0 0-2 2v4a6 6 0 0 0 12 0V5a2 2 0 0 0-2-2h-1",key:"rb5t3r"}],["path",{d:"M8 15a6 6 0 0 0 12 0v-3",key:"x18d4x"}],["circle",{cx:"20",cy:"10",r:"2",key:"ts1r5v"}]]);var d=s(5234),c=s(1382);function x(){return(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsxs)("section",{className:"relative bg-gradient-to-br from-primary-teal via-deep-indigo to-primary-teal-dark text-white py-20 lg:py-32 islamic-pattern",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,a.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"font-serif text-4xl md:text-6xl lg:text-7xl font-bold mb-6",children:"Rediscover Healing"}),(0,a.jsx)("p",{className:"text-xl md:text-2xl lg:text-3xl mb-4 text-primary-teal-light",children:"The El-Gilani Methodology"}),(0,a.jsx)("p",{className:"text-lg md:text-xl max-w-3xl mx-auto mb-8 text-gray-200",children:"A profound system of healing developed by El-Sheikh Syed Mubarik Ali Shah Gilani. This methodology offers a unique, faith-centered approach to understanding and treating a wide range of mental and physical ailments."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsxs)(i(),{href:"/about-sheikh",className:"inline-flex items-center px-8 py-3 bg-accent-gold hover:bg-accent-gold-dark text-white font-semibold rounded-lg transition-colors duration-200",children:["Learn About El-Sheikh",(0,a.jsx)(l.A,{className:"ml-2 w-5 h-5"})]}),(0,a.jsx)(i(),{href:"/core-principles",className:"inline-flex items-center px-8 py-3 border-2 border-white text-white hover:bg-white hover:text-primary-teal font-semibold rounded-lg transition-colors duration-200",children:"Explore EGM Principles"})]})]})})]}),(0,a.jsx)("section",{className:"py-16 lg:py-24 bg-white",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsx)("h2",{className:"font-serif text-3xl md:text-4xl lg:text-5xl font-bold text-dark-gray mb-6",children:"What is the El-Gilani Methodology?"}),(0,a.jsx)("p",{className:"text-lg md:text-xl text-medium-gray max-w-4xl mx-auto",children:"The El-Gilani Methodology is a comprehensive healing system rooted in the wisdom of the Holy Quran and Sufi spiritual sciences. It posits that many illnesses, particularly mental disorders, stem from diseases of the non-physical self, or psyche."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-serif text-2xl md:text-3xl font-semibold text-primary-teal mb-6",children:"A Revolutionary Approach"}),(0,a.jsx)("p",{className:"text-medium-gray mb-6",children:"Unlike treatments that focus solely on the physical body, EGM seeks to purify the soul and restore balance through spiritual means. This methodology recognizes the existence and importance of the non-physical body and its esoteric heart (qalb) in health and illness."}),(0,a.jsx)("p",{className:"text-medium-gray mb-8",children:"“I am not the healer, Allah is the only one who heals and He doesn’t need any reward.” - El-Sheikh Gilani"}),(0,a.jsxs)(i(),{href:"/origins",className:"inline-flex items-center text-primary-teal hover:text-primary-teal-dark font-semibold",children:["Discover the Origins",(0,a.jsx)(l.A,{className:"ml-2 w-4 h-4"})]})]}),(0,a.jsx)("div",{className:"bg-warm-gray p-8 rounded-lg",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(n.A,{className:"w-16 h-16 text-accent-gold mx-auto mb-4"}),(0,a.jsx)("h4",{className:"font-serif text-xl font-semibold text-dark-gray mb-4",children:"Spiritual Healing"}),(0,a.jsx)("p",{className:"text-medium-gray",children:"EGM addresses the root causes of illness through spiritual purification, Quranic therapy, and the restoration of divine connection."})]})})]})]})}),(0,a.jsx)("section",{className:"py-16 lg:py-24 bg-warm-gray",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsx)("h2",{className:"font-serif text-3xl md:text-4xl font-bold text-dark-gray mb-6",children:"Key Differentiators"}),(0,a.jsx)("p",{className:"text-lg text-medium-gray max-w-3xl mx-auto",children:"EGM offers a unique approach that sets it apart from conventional treatments"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm text-center",children:[(0,a.jsx)(n.A,{className:"w-12 h-12 text-primary-teal mx-auto mb-4"}),(0,a.jsx)("h3",{className:"font-semibold text-lg text-dark-gray mb-3",children:"Addresses the Soul"}),(0,a.jsx)("p",{className:"text-medium-gray text-sm",children:"Recognizes the existence and importance of the non-physical body and its esoteric heart (qalb)"})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm text-center",children:[(0,a.jsx)(o,{className:"w-12 h-12 text-accent-gold mx-auto mb-4"}),(0,a.jsx)("h3",{className:"font-semibold text-lg text-dark-gray mb-3",children:"Non-Invasive"}),(0,a.jsx)("p",{className:"text-medium-gray text-sm",children:"Employs no drugs or electroshock therapy, relying on recitations, prayers, and visualizations"})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm text-center",children:[(0,a.jsx)(d.A,{className:"w-12 h-12 text-deep-indigo mx-auto mb-4"}),(0,a.jsx)("h3",{className:"font-semibold text-lg text-dark-gray mb-3",children:"Faith-Based"}),(0,a.jsx)("p",{className:"text-medium-gray text-sm",children:"Grounded in the belief in One Almighty God as the ultimate healer"})]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm text-center",children:[(0,a.jsx)(c.A,{className:"w-12 h-12 text-primary-teal-light mx-auto mb-4"}),(0,a.jsx)("h3",{className:"font-semibold text-lg text-dark-gray mb-3",children:"Holistic"}),(0,a.jsx)("p",{className:"text-medium-gray text-sm",children:"Understands the deep connection between actions, morality, beliefs, and overall well-being"})]})]})]})}),(0,a.jsx)("section",{className:"py-16 lg:py-24 bg-primary-teal text-white",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,a.jsx)("h2",{className:"font-serif text-3xl md:text-4xl font-bold mb-6",children:"Explore the Path to True Healing"}),(0,a.jsx)("p",{className:"text-xl mb-8 max-w-3xl mx-auto",children:"Discover the revolutionary insights of El-Sheikh Gilani, the scientific demonstrations of EGM’s efficacy, and a path to true healing and spiritual understanding."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsxs)(i(),{href:"/case-studies",className:"inline-flex items-center px-8 py-3 bg-accent-gold hover:bg-accent-gold-dark text-white font-semibold rounded-lg transition-colors duration-200",children:["View Case Studies",(0,a.jsx)(l.A,{className:"ml-2 w-5 h-5"})]}),(0,a.jsx)(i(),{href:"/contact",className:"inline-flex items-center px-8 py-3 border-2 border-white text-white hover:bg-white hover:text-primary-teal font-semibold rounded-lg transition-colors duration-200",children:"Get in Touch"})]})]})})]})}},4287:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,5814,23))},5234:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(6373).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},5731:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>d});var a=s(5239),r=s(8088),i=s(8170),l=s.n(i),n=s(893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,4122)),"C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,8042)),"C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6440:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(6373).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},7495:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,721,658,400],()=>s(5731));module.exports=a})();