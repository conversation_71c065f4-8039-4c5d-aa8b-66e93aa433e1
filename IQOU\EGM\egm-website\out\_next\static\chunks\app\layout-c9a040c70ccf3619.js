(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},4856:(e,r,t)=>{Promise.resolve().then(t.t.bind(t,6874,23)),Promise.resolve().then(t.t.bind(t,8575,23)),Promise.resolve().then(t.t.bind(t,5044,23)),Promise.resolve().then(t.t.bind(t,347,23)),Promise.resolve().then(t.bind(t,5284))},5044:e=>{e.exports={style:{fontFamily:"'Playfair Display', 'Playfair Display Fallback'",fontStyle:"normal"},className:"__className_840cdc",variable:"__variable_840cdc"}},5284:(e,r,t)=>{"use strict";t.d(r,{default:()=>g});var a=t(5155),l=t(2115),s=t(6874),i=t.n(s);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),n=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),h=e=>{let r=n(e);return r.charAt(0).toUpperCase()+r.slice(1)},c=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()},m=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let p=(0,l.forwardRef)((e,r)=>{let{color:t="currentColor",size:a=24,strokeWidth:s=2,absoluteStrokeWidth:i,className:o="",children:n,iconNode:h,...p}=e;return(0,l.createElement)("svg",{ref:r,...d,width:a,height:a,stroke:t,strokeWidth:i?24*Number(s)/Number(a):s,className:c("lucide",o),...!n&&!m(p)&&{"aria-hidden":"true"},...p},[...h.map(e=>{let[r,t]=e;return(0,l.createElement)(r,t)}),...Array.isArray(n)?n:[n]])}),u=(e,r)=>{let t=(0,l.forwardRef)((t,a)=>{let{className:s,...i}=t;return(0,l.createElement)(p,{ref:a,iconNode:r,className:c("lucide-".concat(o(h(e))),"lucide-".concat(e),s),...i})});return t.displayName=h(e),t},f=u("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),y=u("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),x=u("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),b=[{href:"/",label:"Home"},{href:"/about-sheikh.html",label:"About El-Sheikh"},{href:"/origins.html",label:"Origins"},{href:"/core-principles.html",label:"Core Principles"},{href:"/treatment-modalities.html",label:"Treatment"},{href:"/diagnostic-techniques.html",label:"Diagnostics"},{href:"/perspective-on-psychiatry.html",label:"Perspective"},{href:"/non-physical-realities.html",label:"Non-Physical Realities"},{href:"/spiritual-basis.html",label:"Spiritual Basis"},{href:"/journey-of-soul.html",label:"Journey of Soul"},{href:"/case-studies.html",label:"Case Studies"},{href:"/physical-ailments.html",label:"Physical Ailments"},{href:"/call-to-humanity.html",label:"Call to Humanity"},{href:"/contact.html",label:"Contact"}];function g(){let[e,r]=(0,l.useState)(!1);return(0,a.jsx)("nav",{className:"bg-white shadow-lg sticky top-0 z-50",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,a.jsxs)(i(),{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-primary-teal to-accent-gold rounded-full flex items-center justify-center",children:(0,a.jsx)(f,{className:"w-5 h-5 text-white"})}),(0,a.jsx)("span",{className:"font-serif font-bold text-xl text-primary-teal",children:"El-Gilani Methodology"})]}),(0,a.jsxs)("div",{className:"hidden lg:flex items-center space-x-1",children:[b.slice(0,7).map(e=>(0,a.jsx)(i(),{href:e.href,className:"px-3 py-2 text-sm font-medium text-gray-700 hover:text-primary-teal hover:bg-warm-gray rounded-md transition-colors duration-200",children:e.label},e.href)),(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("button",{className:"px-3 py-2 text-sm font-medium text-gray-700 hover:text-primary-teal hover:bg-warm-gray rounded-md transition-colors duration-200",children:"More"}),(0,a.jsx)("div",{className:"absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50",children:(0,a.jsx)("div",{className:"py-1",children:b.slice(7).map(e=>(0,a.jsx)(i(),{href:e.href,className:"block px-4 py-2 text-sm text-gray-700 hover:bg-warm-gray hover:text-primary-teal",children:e.label},e.href))})})]})]}),(0,a.jsx)("div",{className:"lg:hidden",children:(0,a.jsx)("button",{onClick:()=>r(!e),className:"p-2 rounded-md text-gray-700 hover:text-primary-teal hover:bg-warm-gray focus:outline-none focus:ring-2 focus:ring-primary-teal",children:e?(0,a.jsx)(y,{className:"w-6 h-6"}):(0,a.jsx)(x,{className:"w-6 h-6"})})})]}),e&&(0,a.jsx)("div",{className:"lg:hidden",children:(0,a.jsx)("div",{className:"px-2 pt-2 pb-3 space-y-1 bg-white border-t border-gray-200",children:b.map(e=>(0,a.jsx)(i(),{href:e.href,className:"block px-3 py-2 text-base font-medium text-gray-700 hover:text-primary-teal hover:bg-warm-gray rounded-md transition-colors duration-200",onClick:()=>r(!1),children:e.label},e.href))})})]})})}},8575:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_d65c78",variable:"__variable_d65c78"}}},e=>{var r=r=>e(e.s=r);e.O(0,[429,874,441,684,358],()=>r(4856)),_N_E=e.O()}]);