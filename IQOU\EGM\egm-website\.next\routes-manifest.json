{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/about-sheikh", "regex": "^/about\\-sheikh(?:/)?$", "routeKeys": {}, "namedRegex": "^/about\\-sheikh(?:/)?$"}, {"page": "/call-to-humanity", "regex": "^/call\\-to\\-humanity(?:/)?$", "routeKeys": {}, "namedRegex": "^/call\\-to\\-humanity(?:/)?$"}, {"page": "/case-studies", "regex": "^/case\\-studies(?:/)?$", "routeKeys": {}, "namedRegex": "^/case\\-studies(?:/)?$"}, {"page": "/contact", "regex": "^/contact(?:/)?$", "routeKeys": {}, "namedRegex": "^/contact(?:/)?$"}, {"page": "/core-principles", "regex": "^/core\\-principles(?:/)?$", "routeKeys": {}, "namedRegex": "^/core\\-principles(?:/)?$"}, {"page": "/diagnostic-techniques", "regex": "^/diagnostic\\-techniques(?:/)?$", "routeKeys": {}, "namedRegex": "^/diagnostic\\-techniques(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/journey-of-soul", "regex": "^/journey\\-of\\-soul(?:/)?$", "routeKeys": {}, "namedRegex": "^/journey\\-of\\-soul(?:/)?$"}, {"page": "/non-physical-realities", "regex": "^/non\\-physical\\-realities(?:/)?$", "routeKeys": {}, "namedRegex": "^/non\\-physical\\-realities(?:/)?$"}, {"page": "/origins", "regex": "^/origins(?:/)?$", "routeKeys": {}, "namedRegex": "^/origins(?:/)?$"}, {"page": "/perspective-on-psychiatry", "regex": "^/perspective\\-on\\-psychiatry(?:/)?$", "routeKeys": {}, "namedRegex": "^/perspective\\-on\\-psychiatry(?:/)?$"}, {"page": "/physical-ailments", "regex": "^/physical\\-ailments(?:/)?$", "routeKeys": {}, "namedRegex": "^/physical\\-ailments(?:/)?$"}, {"page": "/spiritual-basis", "regex": "^/spiritual\\-basis(?:/)?$", "routeKeys": {}, "namedRegex": "^/spiritual\\-basis(?:/)?$"}, {"page": "/treatment-modalities", "regex": "^/treatment\\-modalities(?:/)?$", "routeKeys": {}, "namedRegex": "^/treatment\\-modalities(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Router-Segment-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc", "prefetchSegmentHeader": "Next-Router-Segment-Prefetch", "prefetchSegmentSuffix": ".segment.rsc", "prefetchSegmentDirSuffix": ".segments"}, "rewriteHeaders": {"pathHeader": "x-nextjs-rewritten-path", "queryHeader": "x-nextjs-rewritten-query"}, "rewrites": []}