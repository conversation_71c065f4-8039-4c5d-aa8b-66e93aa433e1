(()=>{var e={};e.id=178,e.ids=[178],e.modules={440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(1658);let i=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},704:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x,metadata:()=>h});var a=s(7413),i=s(4536),r=s.n(i),l=s(6373);let n=(0,l.A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]]),d=(0,l.A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]]);var c=s(5234),o=s(6700),m=s(6440);let h={title:"Origins of the El-Gilani Methodology | EGM History",description:"Discover the origins of the El-Gilani Methodology, from the 1975 WHO challenge to the establishment of the Sufi Psychiatric Research Institute."};function x(){return(0,a.jsxs)("div",{className:"min-h-screen",children:[(0,a.jsxs)("section",{className:"relative bg-gradient-to-br from-accent-gold via-primary-teal to-deep-indigo text-white py-20 lg:py-32",children:[(0,a.jsx)("div",{className:"absolute inset-0 bg-black/20"}),(0,a.jsx)("div",{className:"relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h1",{className:"font-serif text-4xl md:text-6xl lg:text-7xl font-bold mb-6",children:"Origins of EGM"}),(0,a.jsx)("p",{className:"text-xl md:text-2xl mb-8 text-primary-teal-light max-w-4xl mx-auto",children:"From the 1975 WHO Challenge to Revolutionary Healing"})]})})]}),(0,a.jsx)("section",{className:"py-16 lg:py-24 bg-white",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsx)("h2",{className:"font-serif text-3xl md:text-4xl font-bold text-dark-gray mb-6",children:"The Journey Begins"}),(0,a.jsx)("p",{className:"text-lg text-medium-gray max-w-4xl mx-auto",children:"The El-Gilani Methodology emerged from a profound challenge issued by the World Health Organization and El-Sheikh Gilani’s unwavering commitment to demonstrating the healing power of the Holy Quran."})]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("div",{className:"absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-primary-teal hidden md:block"}),(0,a.jsxs)("div",{className:"space-y-12",children:[(0,a.jsxs)("div",{className:"relative flex items-center",children:[(0,a.jsx)("div",{className:"md:w-1/2 md:pr-8 text-right",children:(0,a.jsxs)("div",{className:"bg-warm-gray p-6 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-end mb-4",children:[(0,a.jsx)(n,{className:"w-6 h-6 text-primary-teal mr-2"}),(0,a.jsx)("span",{className:"font-semibold text-accent-gold",children:"1975"})]}),(0,a.jsx)("h3",{className:"font-serif text-xl font-semibold text-dark-gray mb-3",children:"The WHO Challenge"}),(0,a.jsx)("p",{className:"text-medium-gray",children:"The World Health Organization issues a challenge to demonstrate the healing potential of the Holy Quran through scientific methodology, seeking evidence-based approaches to mental health treatment."})]})}),(0,a.jsx)("div",{className:"absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary-teal rounded-full border-4 border-white hidden md:block"}),(0,a.jsx)("div",{className:"md:w-1/2 md:pl-8"})]}),(0,a.jsxs)("div",{className:"relative flex items-center",children:[(0,a.jsx)("div",{className:"md:w-1/2 md:pr-8"}),(0,a.jsx)("div",{className:"absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-accent-gold rounded-full border-4 border-white hidden md:block"}),(0,a.jsx)("div",{className:"md:w-1/2 md:pl-8",children:(0,a.jsxs)("div",{className:"bg-warm-gray p-6 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(d,{className:"w-6 h-6 text-accent-gold mr-2"}),(0,a.jsx)("span",{className:"font-semibold text-primary-teal",children:"The Response"})]}),(0,a.jsx)("h3",{className:"font-serif text-xl font-semibold text-dark-gray mb-3",children:"Accepting the Challenge"}),(0,a.jsx)("p",{className:"text-medium-gray",children:"El-Sheikh Syed Mubarik Ali Shah Gilani accepts the WHO challenge, beginning an intensive search for Quranic cures and developing what would become the El-Gilani Methodology."})]})})]}),(0,a.jsxs)("div",{className:"relative flex items-center",children:[(0,a.jsx)("div",{className:"md:w-1/2 md:pr-8 text-right",children:(0,a.jsxs)("div",{className:"bg-warm-gray p-6 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center justify-end mb-4",children:[(0,a.jsx)(c.A,{className:"w-6 h-6 text-deep-indigo mr-2"}),(0,a.jsx)("span",{className:"font-semibold text-deep-indigo",children:"Research Phase"})]}),(0,a.jsx)("h3",{className:"font-serif text-xl font-semibold text-dark-gray mb-3",children:"Developing the Methodology"}),(0,a.jsx)("p",{className:"text-medium-gray",children:"Years of intensive research into Quranic healing, Sufi spiritual sciences, and the understanding of the non-physical body lead to the development of comprehensive diagnostic and treatment approaches."})]})}),(0,a.jsx)("div",{className:"absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-deep-indigo rounded-full border-4 border-white hidden md:block"}),(0,a.jsx)("div",{className:"md:w-1/2 md:pl-8"})]}),(0,a.jsxs)("div",{className:"relative flex items-center",children:[(0,a.jsx)("div",{className:"md:w-1/2 md:pr-8"}),(0,a.jsx)("div",{className:"absolute left-1/2 transform -translate-x-1/2 w-4 h-4 bg-primary-teal rounded-full border-4 border-white hidden md:block"}),(0,a.jsx)("div",{className:"md:w-1/2 md:pl-8",children:(0,a.jsxs)("div",{className:"bg-warm-gray p-6 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)(o.A,{className:"w-6 h-6 text-primary-teal mr-2"}),(0,a.jsx)("span",{className:"font-semibold text-accent-gold",children:"Establishment"})]}),(0,a.jsx)("h3",{className:"font-serif text-xl font-semibold text-dark-gray mb-3",children:"Sufi Psychiatric Research Institute"}),(0,a.jsx)("p",{className:"text-medium-gray",children:"The establishment of the Sufi Psychiatric Research Institute to formally document, research, and demonstrate the efficacy of the El-Gilani Methodology through scientific observation and case studies."})]})})]})]})]})]})}),(0,a.jsx)("section",{className:"py-16 lg:py-24 bg-warm-gray",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"font-serif text-3xl md:text-4xl font-bold text-dark-gray mb-6",children:"The Search for Quranic Cure"}),(0,a.jsx)("p",{className:"text-lg text-medium-gray mb-6",children:"El-Sheikh Gilani’s response to the WHO challenge was not merely academic but deeply spiritual. He embarked on an intensive search through the Holy Quran, seeking not just verses that mention healing, but understanding the deeper spiritual mechanisms of cure."}),(0,a.jsx)("p",{className:"text-lg text-medium-gray mb-6",children:"This search revealed profound insights into the nature of illness and health, particularly the recognition that many ailments stem from diseases of the non-physical self - the soul and its esoteric heart (qalb)."}),(0,a.jsx)("p",{className:"text-lg text-medium-gray mb-8",children:"The methodology that emerged represents a synthesis of Quranic wisdom, Sufi spiritual sciences, and practical therapeutic approaches that address the root causes of suffering."}),(0,a.jsxs)(r(),{href:"/core-principles",className:"inline-flex items-center text-primary-teal hover:text-primary-teal-dark font-semibold",children:["Explore Core Principles",(0,a.jsx)(m.A,{className:"ml-2 w-4 h-4"})]})]}),(0,a.jsxs)("div",{className:"bg-white p-8 rounded-lg shadow-sm",children:[(0,a.jsx)("h3",{className:"font-serif text-2xl font-semibold text-dark-gray mb-6 text-center",children:"Key Discoveries"}),(0,a.jsxs)("ul",{className:"space-y-4",children:[(0,a.jsxs)("li",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-primary-teal rounded-full mt-2 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-medium-gray",children:"Recognition of the non-physical body as the seat of many illnesses"})]}),(0,a.jsxs)("li",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-accent-gold rounded-full mt-2 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-medium-gray",children:"Understanding of the qalb (esoteric heart) and its role in spiritual health"})]}),(0,a.jsxs)("li",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-deep-indigo rounded-full mt-2 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-medium-gray",children:"Development of diagnostic techniques based on Sufi discernment"})]}),(0,a.jsxs)("li",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"w-2 h-2 bg-primary-teal rounded-full mt-2 flex-shrink-0"}),(0,a.jsx)("span",{className:"text-medium-gray",children:"Creation of treatment modalities using Quranic recitations and spiritual practices"})]})]})]})]})})}),(0,a.jsx)("section",{className:"py-16 lg:py-24 bg-white",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"text-center mb-16",children:[(0,a.jsx)("h2",{className:"font-serif text-3xl md:text-4xl font-bold text-dark-gray mb-6",children:"A Revolutionary Approach"}),(0,a.jsx)("p",{className:"text-lg text-medium-gray max-w-4xl mx-auto",children:"The El-Gilani Methodology represents a paradigm shift in understanding health and healing, offering hope where conventional treatments have failed."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-primary-teal rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(c.A,{className:"w-8 h-8 text-white"})}),(0,a.jsx)("h3",{className:"font-semibold text-lg text-dark-gray mb-3",children:"Scientific Validation"}),(0,a.jsx)("p",{className:"text-medium-gray text-sm",children:"Documented case studies and scientific observation of healing outcomes"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-accent-gold rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(d,{className:"w-8 h-8 text-white"})}),(0,a.jsx)("h3",{className:"font-semibold text-lg text-dark-gray mb-3",children:"Holistic Approach"}),(0,a.jsx)("p",{className:"text-medium-gray text-sm",children:"Addresses spiritual, mental, and physical dimensions of health simultaneously"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-deep-indigo rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(o.A,{className:"w-8 h-8 text-white"})}),(0,a.jsx)("h3",{className:"font-semibold text-lg text-dark-gray mb-3",children:"Institutional Support"}),(0,a.jsx)("p",{className:"text-medium-gray text-sm",children:"Formal research institute dedicated to advancing and documenting the methodology"})]})]})]})}),(0,a.jsx)("section",{className:"py-16 lg:py-24 bg-primary-teal text-white",children:(0,a.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center",children:[(0,a.jsx)("h2",{className:"font-serif text-3xl md:text-4xl font-bold mb-6",children:"Discover the Methodology"}),(0,a.jsx)("p",{className:"text-xl mb-8 max-w-3xl mx-auto",children:"Learn how this revolutionary approach addresses the spiritual dimensions of health and healing."}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsxs)(r(),{href:"/treatment-modalities",className:"inline-flex items-center px-8 py-3 bg-accent-gold hover:bg-accent-gold-dark text-white font-semibold rounded-lg transition-colors duration-200",children:["Treatment Methods",(0,a.jsx)(m.A,{className:"ml-2 w-5 h-5"})]}),(0,a.jsx)(r(),{href:"/case-studies",className:"inline-flex items-center px-8 py-3 border-2 border-white text-white hover:bg-white hover:text-primary-teal font-semibold rounded-lg transition-colors duration-200",children:"View Case Studies"})]})]})})]})}},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},4287:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,5814,23))},4291:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>c});var a=s(5239),i=s(8088),r=s(8170),l=s.n(r),n=s(893),d={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);s.d(t,d);let c={children:["",{children:["origins",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,704)),"C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\origins\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,8042)),"C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\AI Coding Projects\\IQOU\\EGM\\egm-website\\src\\app\\origins\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/origins/page",pathname:"/origins",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5234:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(6373).A)("book-open",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},6440:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(6373).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},6700:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(6373).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},7495:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4536,23))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[447,721,658,400],()=>s(4291));module.exports=a})();