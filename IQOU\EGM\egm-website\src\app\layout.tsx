import type { Metadata } from "next";
import { Inter, Playfair_Display } from "next/font/google";
import "./globals.css";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

const playfair = Playfair_Display({
  variable: "--font-playfair",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "El-Gilani Methodology (EGM) - Revolutionary Healing Through Islamic Spiritual Sciences",
  description: "Discover the El-Gilani Methodology, a profound system of healing developed by El-Sheikh <PERSON>. This faith-centered approach addresses mental and physical ailments through Quranic therapy and Sufi spiritual sciences.",
  keywords: "El-Gilani Methodology, EGM, Islamic healing, Sufi therapy, Quranic psychiatry, spiritual healing, mental health, El-<PERSON>",
  authors: [{ name: "El-Sheikh <PERSON>" }],
  openGraph: {
    title: "El-Gilani Methodology (EGM) - Revolutionary Healing",
    description: "A comprehensive healing system rooted in Islamic spiritual sciences and Quranic therapy.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "El-Gilani Methodology (EGM)",
    description: "Revolutionary healing through Islamic spiritual sciences",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className="scroll-smooth">
      <body
        className={`${inter.variable} ${playfair.variable} antialiased min-h-screen flex flex-col`}
      >
        <Navigation />
        <main className="flex-grow">
          {children}
        </main>
        <Footer />
      </body>
    </html>
  );
}
